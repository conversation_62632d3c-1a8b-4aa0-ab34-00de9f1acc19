import urllib.request
import urllib.error
import time
import random
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed

def download_file_with_retry(url, output_filename, thread_id, max_retries=0, initial_delay=1, success_queue=None):
    """
    Download a file from URL with automatic retry on 502 errors
    
    Args:
        url: The URL to download from
        output_filename: Where to save the downloaded file
        thread_id: Thread identifier for logging
        max_retries: Maximum number of retries (0 means retry forever)
        initial_delay: Initial delay between retries (will increase with backoff)
        success_queue: Queue to signal successful download
    """
    attempt = 0
    delay = initial_delay
    
    while max_retries == 0 or attempt < max_retries:
        # Check if another thread has already succeeded
        if success_queue and not success_queue.empty():
            print(f"Thread {thread_id}: Another thread succeeded, stopping...")
            return False
            
        try:
            print(f"Thread {thread_id}: Download attempt {attempt+1}...")
            urllib.request.urlretrieve(url, f"{output_filename}.thread{thread_id}")
            print(f"Thread {thread_id}: Download successful! File saved as {output_filename}.thread{thread_id}")
            
            # Signal success to other threads
            if success_queue:
                success_queue.put(thread_id)
            return True
            
        except urllib.error.HTTPError as e:
            if e.code == 502:
                attempt += 1
                print(f"Thread {thread_id}: Encountered 502 error. Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
                # Add some randomness and increase delay for next attempt (exponential backoff)
                # Cap the delay at 8 seconds maximum
                delay = min(8, delay * 1.5 + random.uniform(0, 1))
            else:
                print(f"Thread {thread_id}: HTTP Error: {e.code} - {e.reason}")
                return False
        except urllib.error.URLError as e:
            print(f"Thread {thread_id}: URL Error: {e.reason}")
            return False
    
    print(f"Thread {thread_id}: Failed to download after {attempt} attempts")
    return False

def download_with_multiple_threads(url, output_filename, num_threads=4):
    """
    Download file using multiple threads for better success rate
    
    Args:
        url: The URL to download from
        output_filename: Base filename for the downloaded file
        num_threads: Number of threads to use
    """
    print(f"Starting download with {num_threads} threads...")
    success_queue = queue.Queue()
    
    # Use ThreadPoolExecutor to manage threads
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Submit download tasks
        futures = []
        for i in range(num_threads):
            future = executor.submit(
                download_file_with_retry, 
                url, 
                output_filename, 
                i+1, 
                max_retries=0,  # Retry indefinitely
                initial_delay=random.uniform(0.5, 2.0),  # Random initial delay
                success_queue=success_queue
            )
            futures.append(future)
        
        # Wait for first success or all failures
        try:
            for future in as_completed(futures):
                result = future.result()
                if result:
                    print("Download completed successfully!")
                    # Cancel remaining tasks
                    for f in futures:
                        f.cancel()
                    return True
        except KeyboardInterrupt:
            print("\nDownload interrupted by user")
            return False
    
    print("All threads failed to download the file")
    return False

if __name__ == "__main__":
    # Configuration
    url = "http://f01.lianty.cn:8084/qztDownload?url=d96802bb4f7d99225e29fe67637fda92&fontId=d96802bb4f7d99225e29fe67637fda92"
    output_file = "downloaded_file"
    num_threads = 6  # Number of concurrent download threads
    
    print("=== Multi-threaded Download with Retry ===")
    print(f"URL: {url}")
    print(f"Output file base: {output_file}")
    print(f"Number of threads: {num_threads}")
    print("=" * 50)
    
    success = download_with_multiple_threads(url, output_file, num_threads)
    
    if success:
        print("\n✅ Download completed successfully!")
    else:
        print("\n❌ Download failed on all threads")
