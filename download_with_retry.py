import urllib.request
import urllib.error
import time
import random

def download_file_with_retry(url, output_filename, max_retries=10, initial_delay=1):
    """
    Download a file from URL with automatic retry on 502 errors

    Args:
        url: The URL to download from
        output_filename: Where to save the downloaded file
        max_retries: Maximum number of retries (0 means retry forever)
        initial_delay: Initial delay between retries (will increase with backoff)
    """
    attempt = 0
    delay = initial_delay

    while max_retries == 0 or attempt < max_retries:
        try:
            print(f"Download attempt {attempt+1}...")
            urllib.request.urlretrieve(url, output_filename)
            print(f"Download successful! File saved as {output_filename}")
            return True
        except urllib.error.HTTPError as e:
            if e.code == 502:
                attempt += 1
                print(f"Encountered 502 error. Retrying in {delay} seconds...")
                time.sleep(delay)
                # Add some randomness and increase delay for next attempt (exponential backoff)
                # Cap the delay at 8 seconds maximum
                delay = min(3, delay * 1.5 + random.uniform(0, 1))
            else:
                print(f"HTTP Error: {e.code} - {e.reason}")
                return False
        except urllib.error.URLError as e:
            print(f"URL Error: {e.reason}")
            return False

    print(f"Failed to download after {attempt} attempts")
    return False

# Example usage
url = "http://f01.lianty.cn:8084/qztDownload?url=d96802bb4f7d99225e29fe67637fda92&fontId=d96802bb4f7d99225e29fe67637fda92"
output_file = "downloaded_file.dat"

download_file_with_retry(url, output_file, max_retries=0)  # 0 means retry indefinitely